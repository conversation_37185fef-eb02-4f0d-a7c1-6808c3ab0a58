<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\Employee;
use App\Models\UserDetail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class FillCompanyOrEmployeeTableJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        DB::beginTransaction();
        try {
            $users = User::whereDoesntHave('companies')
             ->orWhereDoesntHave('employee')
             ->get();
            // create a log to list how many users affected
            Log::info("Total users affected: " . $users->count());
            foreach ($users as $user) {
                if($user->isBizappUser == 'Y'){
                    $this->processUserBizapp($user);
                } else {
                    $this->processUserStandalone($user);
                }
            }

            DB::commit();
            Log::info("Companies and employees update completed successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error in updateCompaniesAndEmployees: " . $e->getMessage());
            throw $e;
        }
    }

    public function processUserBizapp($user)
    {
        Log::info("Processing user: {$user->username}");
        $response = Http::asForm()->post('https://corrad.visionice.net/bizapp/apigenerator_VERSIPOS.php?api_name=TRACK_LOGIN', [
            'username' => $user->username,
            'password' => config('bizappos.bizapp_mpw'),
            'DOMAIN' => $user->domain,
            'platform' => 'POS'
        ]);

        if (!$response->successful()) {
            Log::error("API call failed for user: {$user->username}");
            return;
        }

        // $result = $response->json();
        $result = json_decode($response->body(), true);
        Log::debug("API response for user {$user->username}: " . json_encode($result));
        if (empty($result) || !isset($result[0]['pid']) || !isset($result[0]['pidboss'])) {
            Log::error("Invalid API response for user: {$user->username}");
            return;
        }

        if ($result[0]['pid'] === $result[0]['pidboss']) {
            // User is the boss, create company for them
            Company::updateOrCreate([
                'user_id' => $user->id,
            ], [
                'com_name' => $result[0]['namaboss'],
                'com_address' => $result[0]['alamat1boss'] . ', ' . $result[0]['alamat2boss'] . ', ' . $result[0]['alamat3boss'],
                'com_state' => $result[0]['negeriboss'],
                'com_postcode' => $result[0]['poskodboss'],
                'com_mobile' => $result[0]['nohpboss'],
                'com_email' => $result[0]['emelboss'],
                'com_country' => $result[0]['country'],
                'account_type' => 'business',
                'ic_number' => '************'
            ]);
            return;
        } else {
            $boss = User::where('pid',$result[0]['pidboss'])->first();
            // if boss does not have account yet then create one
            if(!$boss){
                $boss = User::create([
                    'username' => $result[0]['penggunaidboss'],
                    'password' => 'temppassword',
                    'email' => $result[0]['emelboss'],
                    'isBizappUser' => 'Y',
                    'domain' => $user->domain,
                    'pid' => $result[0]['pidboss']
                ]);
    
                UserDetail::updateOrCreate([
                    'user_id' => $boss->id, // Fixed: should be boss's user_id, not current user's
                ], [
                    'first_name' => $result[0]['namaboss'],
                    'mobile' => $result[0]['nohpboss'],
                    'avatar' => $result[0]['attachmentphoto'],
                    'address' => $result[0]['alamat1boss'] . ', ' . $result[0]['alamat2boss'] . ', ' . $result[0]['alamat3boss'],
                    'currency' => $result[0]['currency'],
                    'bizapp_secretkey' => $result[0]['secretkey'],
                    'state' => $result[0]['negeriboss'],
                    'postcode' => $result[0]['poskodboss'],
                    'country' => $result[0]['negaraboss']
                ]);

                Company::updateOrCreate([
                    'user_id' => $boss->id, // Fixed: should be boss's user_id, not current user's
                ], [
                    'com_name' => $result[0]['namaboss'],
                    'com_address' => $result[0]['alamat1boss'] . ', ' . $result[0]['alamat2boss'] . ', ' . $result[0]['alamat3boss'],
                    'com_state' => $result[0]['negeriboss'],
                    'com_postcode' => $result[0]['poskodboss'],
                    'com_mobile' => $result[0]['nohpboss'],
                    'com_email' => $result[0]['emelboss'],
                    'com_country' => $result[0]['country'],
                    'account_type' => 'business',
                    'ic_number' => '************'
                ]);
            }
            $company = Company::where('user_id',$boss->id)->first();

            // Ensure boss has userDetails before creating receipt
            $bossUserDetails = $boss->userDetails;
            if (!$bossUserDetails) {
                Log::warning("Boss user {$boss->id} doesn't have userDetails, creating receipt with default values");
            }

            Receipt::create([
                'company_id' => $company->id,
                'user_id' => $boss->id,
                'title' => $result[0]['penggunaid'],
                'name' => $bossUserDetails ? $bossUserDetails->first_name : $result[0]['namaboss'],
                'email' => $boss->email,
                'phone' => $bossUserDetails ? $bossUserDetails->mobile : $result[0]['nohpboss'],
                'address' => $company->com_address,
                'city' => $company->com_city,
                'state' => $company->com_state,
                'country' => $company->com_country,
                'postcode' => $company->com_postcode,
                'sst' => '0,0'
            ]);


            if ($company) {
                $employee = Employee::updateOrCreate([
                    'user_id' => $user->id,
                    'boss_id' => $boss->id,
                    'company_id' => $company->id,
                    'emp_jobTitle' => 'staff',
                ]);
                $employee->save();
            } else {
                Log::error("Company not found for boss: {$boss->id}");
            }
        }
    }

    public function processUserStandalone($user)
    {
        Log::info("Processing standalone user: {$user->username}");

        // Check if user already has company or employee relations
        $hasCompany = $user->companies()->exists();
        $hasEmployee = $user->employee()->exists();

        if ($hasCompany && $hasEmployee) {
            Log::info("User {$user->username} already has both company and employee relations");
            return;
        }

        // Create UserDetail if it doesn't exist
        if (!$user->userDetails) {
            $userDetail = UserDetail::create([
                'user_id' => $user->id,
                'first_name' => $user->username, // Use username as fallback
                'mobile' => '**********', // Default mobile number
                'address' => 'Malaysia',
                'city' => 'Kuala Lumpur',
                'state' => 'Kuala Lumpur',
                'postcode' => '50000',
                'country' => 'Malaysia',
                'currency' => 'MYR',
                'status' => 'active'
            ]);
            Log::info("Created UserDetail for standalone user: {$user->username}");
        }

        // Create Company if user doesn't have one
        if (!$hasCompany) {
            $company = Company::create([
                'user_id' => $user->id,
                'com_name' => $user->username . ' Company', // Default company name
                'com_address' => 'Malaysia',
                'com_city' => 'Kuala Lumpur',
                'com_state' => 'Kuala Lumpur',
                'com_postcode' => '50000',
                'com_country' => 'Malaysia',
                'com_mobile' => '**********',
                'com_email' => $user->email,
                'account_type' => 'individual', // Default for standalone users
                'ic_number' => '************', // Default IC number
                'status' => 'active'
            ]);

            Log::info("Created Company for standalone user: {$user->username}");

            // Create Receipt for the company
            Receipt::create([
                'company_id' => $company->id,
                'user_id' => $user->id,
                'title' => $user->username,
                'name' => $user->userDetails->first_name ?? $user->username,
                'email' => $user->email,
                'phone' => $user->userDetails->mobile ?? '**********',
                'address' => $company->com_address,
                'city' => $company->com_city,
                'state' => $company->com_state,
                'country' => $company->com_country,
                'postcode' => $company->com_postcode,
                'sst' => '0,0'
            ]);

            Log::info("Created Receipt for standalone user: {$user->username}");
        }

        // For standalone users, they are their own boss, so no employee record needed
        // unless they specifically need to be treated as an employee of their own company
        if (!$hasEmployee && !$hasCompany) {
            // Only create employee record if they didn't have a company before
            // This makes them an employee of their own company
            $company = $user->companies; // Get the company we just created

            if ($company) {
                Employee::create([
                    'user_id' => $user->id,
                    'boss_id' => $user->id, // They are their own boss
                    'company_id' => $company->id,
                    'emp_jobTitle' => 'owner',
                    'emp_status' => 'active',
                    'total_sales' => 0,
                    'join_date' => now()
                ]);

                Log::info("Created Employee record for standalone user: {$user->username}");
            }
        }

        Log::info("Completed processing standalone user: {$user->username}");
    }
}
