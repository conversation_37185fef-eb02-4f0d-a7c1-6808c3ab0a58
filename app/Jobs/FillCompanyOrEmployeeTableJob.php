<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\Employee;
use App\Models\UserDetail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class FillCompanyOrEmployeeTableJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        DB::beginTransaction();
        try {
            $users = User::whereDoesntHave('companies')
             ->orWhereDoesntHave('employee')
             ->get();
            // create a log to list how many users affected
            Log::info("Total users affected: " . $users->count());
            foreach ($users as $user) {
                if($user->isBizappUser == 'Y'){
                    $this->processUserBizapp($user);
                } else {
                    
                }
            }

            DB::commit();
            Log::info("Companies and employees update completed successfully.");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error in updateCompaniesAndEmployees: " . $e->getMessage());
            throw $e;
        }
    }

    public function processUserBizapp($user)
    {
        Log::info("Processing user: {$user->username}");
        $response = Http::asForm()->post('https://corrad.visionice.net/bizapp/apigenerator_VERSIPOS.php?api_name=TRACK_LOGIN', [
            'username' => $user->username,
            'password' => config('bizappos.bizapp_mpw'),
            'DOMAIN' => $user->domain,
            'platform' => 'POS'
        ]);

        if (!$response->successful()) {
            Log::error("API call failed for user: {$user->username}");
            return;
        }

        // $result = $response->json();
        $result = json_decode($response->body(), true);
        Log::debug("API response for user {$user->username}: " . json_encode($result));
        if (empty($result) || !isset($result[0]['pid']) || !isset($result[0]['pidboss'])) {
            Log::error("Invalid API response for user: {$user->username}");
            return;
        }

        if ($result[0]['pid'] === $result[0]['pidboss']) {
            Company::updateOrCreate([
              'user_id' => $user->id, ],[
            'com_name' => $result[0]['namaboss'],
            'com_address' => $result[0]['alamat1boss'] . ', ' . $result[0]['alamat2boss'] . ', ' . $result[0]['alamat3boss'],
            'com_state' => $result[0]['negeriboss'],
            'com_postcode' => $result[0]['poskodboss'],
            'com_mobile' => $result[0]['nohpboss'],
            'com_email' => $result[0]['emelboss'],
            'com_country' => $result[0]['country']
              ]);
            return;
        } else {
            $boss = User::where('pid',$result[0]['pidboss'])->first();
            // if boss does not have account yet then create one
            if(!$boss){
                $boss = User::create([
                    'username' => $result[0]['penggunaidboss'],
                    'password' => 'temppassword',
                    'email' => $result[0]['emelboss'],
                    'isBizappUser' => 'Y',
                    'domain' => $user->domain,
                    'pid' => $result[0]['pidboss']
                ]);
    
                UserDetail::updateOrCreate([
                    'user_id' => $user->id,
                    'first_name' => $result[0]['namaboss'],
                    'mobile' => $result[0]['nohpboss'],
                    'avatar' => $result[0]['attachmentphoto'],
                    'address' => $result[0]['alamat1boss'] . ', ' . $result[0]['alamat2boss'] . ', ' . $result[0]['alamat3boss'],
                    'currency' => $result[0]['currency'],
                    'bizapp_secretkey' => $result[0]['secretkey'],
                    'state' => $result[0]['negeriboss'],
                    'postcode' => $result[0]['poskodboss'],
                    'country' => $result[0]['negaraboss']
                ]);

                Company::updateOrCreate([
                    'user_id' => $user->id, ],[
                  'com_name' => $result[0]['namaboss'],
                  'com_address' => $result[0]['alamat1boss'] . ', ' . $result[0]['alamat2boss'] . ', ' . $result[0]['alamat3boss'],
                  'com_state' => $result[0]['negeriboss'],
                  'com_postcode' => $result[0]['poskodboss'],
                  'com_mobile' => $result[0]['nohpboss'],
                  'com_email' => $result[0]['emelboss'],
                  'com_country' => $result[0]['country'],
                  'account_type' => 'business',
                  'ic_number' => '************'
                    ]);
            }
            $company = Company::where('user_id',$boss->id)->first();
            Receipt::create([
                'company_id' => $company->id,
                'user_id' => $boss->id,
                'title' => $result[0]['penggunaid'],
                'name' => $boss->userDetails->first_name,
                'email' => $boss->email,
                'phone' => $boss->userDetails->mobile,
                'address' => $company->com_address,
                'city' => $company->com_city,
                'state' => $company->com_state,
                'country' => $company->com_country,
                'postcode' => $company->com_postcode,
                'sst' => '0,0'
            ]);


            if ($company) {
                $employee = Employee::updateOrCreate([
                    'user_id' => $user->id,
                    'boss_id' => $boss->id,
                    'company_id' => $company->id,
                    'emp_jobTitle' => 'staff',
                ]);
                $employee->save();
            } else {
                Log::error("Company not found for boss: {$boss->id}");
            }
        }
    }

    public function processUserStandalone()
    {

    }
}
