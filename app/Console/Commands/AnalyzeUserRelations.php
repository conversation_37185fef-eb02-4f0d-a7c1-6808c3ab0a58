<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class AnalyzeUserRelations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:analyze-relations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze user relations distribution to understand data patterns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== USER RELATIONS ANALYSIS ===');
        
        // Basic counts
        $totalUsers = User::count();
        $usersWithCompanies = User::whereHas('companies')->count();
        $usersWithEmployees = User::whereHas('employee')->count();
        $usersWithBoth = User::whereHas('companies')->whereHas('employee')->count();
        $usersWithNeither = User::whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        $usersWithEither = User::where(function($query) {
            $query->whereHas('companies')->orWhereHas('employee');
        })->count();
        
        // User type breakdown
        $bizappUsers = User::where('isBizappUser', 'Y')->count();
        $standaloneUsers = User::where('isBizappUser', '!=', 'Y')->count();
        
        // Bizapp user relations
        $bizappWithCompanies = User::where('isBizappUser', 'Y')->whereHas('companies')->count();
        $bizappWithEmployees = User::where('isBizappUser', 'Y')->whereHas('employee')->count();
        $bizappWithBoth = User::where('isBizappUser', 'Y')->whereHas('companies')->whereHas('employee')->count();
        $bizappWithNeither = User::where('isBizappUser', 'Y')->whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        
        // Standalone user relations
        $standaloneWithCompanies = User::where('isBizappUser', '!=', 'Y')->whereHas('companies')->count();
        $standaloneWithEmployees = User::where('isBizappUser', '!=', 'Y')->whereHas('employee')->count();
        $standaloneWithBoth = User::where('isBizappUser', '!=', 'Y')->whereHas('companies')->whereHas('employee')->count();
        $standaloneWithNeither = User::where('isBizappUser', '!=', 'Y')->whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        
        // OLD QUERY (problematic)
        $oldQueryCount = User::whereDoesntHave('companies')->orWhereDoesntHave('employee')->count();
        
        // NEW QUERY (fixed)
        $newQueryCount = User::whereDoesntHave('companies')->whereDoesntHave('employee')->count();
        
        $this->table(['Metric', 'Count', 'Percentage'], [
            ['Total Users', $totalUsers, '100%'],
            ['Users with Companies', $usersWithCompanies, round(($usersWithCompanies/$totalUsers)*100, 1).'%'],
            ['Users with Employee Records', $usersWithEmployees, round(($usersWithEmployees/$totalUsers)*100, 1).'%'],
            ['Users with BOTH Relations', $usersWithBoth, round(($usersWithBoth/$totalUsers)*100, 1).'%'],
            ['Users with EITHER Relation', $usersWithEither, round(($usersWithEither/$totalUsers)*100, 1).'%'],
            ['Users with NEITHER Relation', $usersWithNeither, round(($usersWithNeither/$totalUsers)*100, 1).'%'],
        ]);
        
        $this->info('');
        $this->info('=== USER TYPE BREAKDOWN ===');
        $this->table(['User Type', 'Total', 'With Companies', 'With Employees', 'With Both', 'With Neither'], [
            [
                'Bizapp Users', 
                $bizappUsers, 
                $bizappWithCompanies, 
                $bizappWithEmployees, 
                $bizappWithBoth, 
                $bizappWithNeither
            ],
            [
                'Standalone Users', 
                $standaloneUsers, 
                $standaloneWithCompanies, 
                $standaloneWithEmployees, 
                $standaloneWithBoth, 
                $standaloneWithNeither
            ]
        ]);
        
        $this->info('');
        $this->info('=== QUERY COMPARISON ===');
        $this->table(['Query Type', 'Count', 'Percentage of Total'], [
            ['OLD: whereDoesntHave(companies) OR whereDoesntHave(employee)', $oldQueryCount, round(($oldQueryCount/$totalUsers)*100, 1).'%'],
            ['NEW: whereDoesntHave(companies) AND whereDoesntHave(employee)', $newQueryCount, round(($newQueryCount/$totalUsers)*100, 1).'%'],
        ]);
        
        $this->info('');
        if ($oldQueryCount > ($totalUsers * 0.7)) {
            $this->error("❌ OLD QUERY ISSUE: Returns {$oldQueryCount} users ({$oldQueryCount/$totalUsers*100:.1f}% of total)");
            $this->error("   This is too many users and indicates the OR logic is wrong.");
        }
        
        if ($newQueryCount < ($totalUsers * 0.5)) {
            $this->info("✅ NEW QUERY LOOKS GOOD: Returns {$newQueryCount} users ({$newQueryCount/$totalUsers*100:.1f}% of total)");
            $this->info("   This is a reasonable subset that actually needs processing.");
        }
        
        $this->info('');
        $this->info('=== RECOMMENDATIONS ===');
        if ($bizappWithNeither > 0) {
            $this->warn("• {$bizappWithNeither} Bizapp users have no company or employee relations");
        }
        if ($standaloneWithNeither > 0) {
            $this->warn("• {$standaloneWithNeither} Standalone users have no company or employee relations");
        }
        
        return 0;
    }
}
